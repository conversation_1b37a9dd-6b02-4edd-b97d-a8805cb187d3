#!/usr/bin/env python3
"""
Script para verificar se todos os arquivos das análises foram gerados corretamente
"""

import os

def verificar_arquivos():
    """
    Verifica se os arquivos esperados foram gerados
    """
    print("📊 VERIFICANDO ARQUIVOS GERADOS")
    print("=" * 50)
    
    arquivos_esperados = {
        'MM': [
            'results/figures/mm_analysis',
            'results/csv/mm_data'
        ],
        'Butterworth': [
            'results/figures/butterworth_analysis',
            'results/csv/butterworth_data'
        ],
        'XGBoost': [
            'results/figures/xgboost_analysis',
            'results/csv/xgboost_analysis',
            'results/models/xgboost_analysis/modelo_multiclasse.pkl',
            'results/models/xgboost_analysis/resumo_treinamento.txt'
        ]
    }
    
    total_arquivos = 0
    arquivos_encontrados = 0
    
    for analise, arquivos in arquivos_esperados.items():
        print(f"\n📈 {analise}:")
        for arquivo in arquivos:
            total_arquivos += 1
            if os.path.exists(arquivo):
                if os.path.isdir(arquivo):
                    # Contar arquivos no diretório
                    try:
                        num_arquivos = len([f for f in os.listdir(arquivo) if os.path.isfile(os.path.join(arquivo, f))])
                        print(f"   ✅ {arquivo}/ ({num_arquivos} arquivos)")
                    except:
                        print(f"   ✅ {arquivo}/ (diretório existe)")
                else:
                    try:
                        tamanho = os.path.getsize(arquivo)
                        print(f"   ✅ {arquivo} ({tamanho:,} bytes)")
                    except:
                        print(f"   ✅ {arquivo} (arquivo existe)")
                arquivos_encontrados += 1
            else:
                print(f"   ❌ {arquivo} - NÃO ENCONTRADO")
    
    print(f"\n📊 Resumo: {arquivos_encontrados}/{total_arquivos} arquivos/diretórios encontrados")
    
    # Verificar CSV do XGBoost especificamente
    if os.path.exists('results/sinais_xgboost_completo.csv'):
        import pandas as pd
        try:
            df = pd.read_csv('results/sinais_xgboost_completo.csv')
            print(f"\n🤖 XGBoost CSV:")
            print(f"   📊 Registros: {len(df):,}")
            print(f"   📈 Ações: {df['Ticker'].nunique()}")
            print(f"   🟢 Sinais de Compra: {df['Sinal_Compra'].sum():,}")
            print(f"   🔴 Sinais de Venda: {df['Sinal_Venda'].sum():,}")
        except Exception as e:
            print(f"   ❌ Erro ao ler CSV: {e}")
    
    return arquivos_encontrados, total_arquivos

if __name__ == "__main__":
    encontrados, total = verificar_arquivos()
    
    if encontrados == total:
        print(f"\n✅ TODOS OS ARQUIVOS ENCONTRADOS!")
    else:
        print(f"\n⚠️ {total - encontrados} arquivos/diretórios não encontrados")
