RESUMO DO TREINAMENTO XGBOOST MULTICLASSE - SINAIS DE TRADING
======================================================================

Data de treinamento: 2025-07-09 13:18:07

MODELO MULTICLASSE:
  • Tipo: XGBoost Multiclasse
  • Classes: 0=Sem Ação, 1=Compra, 2=Venda
  • Função de perda: Cross-entropy (mlogloss)
  • Features: pct_change da média OHLC (variação percentual)
  • Acurácia geral: 0.491

CONFIGURAÇÕES UTILIZADAS:
  • Período de dados: 5y
  • Horizonte de sinais: 1 dias
  • Lags OHLC: 10
  • Janela volatilidade: 20
  • Multiplicador spread: 0.5

FEATURES UTILIZADAS (13):
   1. Media_OHLC_PctChange_Lag_1
   2. Media_OHLC_PctChange_Lag_2
   3. Media_OHLC_PctChange_Lag_3
   4. Media_OHLC_PctChange_Lag_4
   5. Media_OHLC_PctChange_Lag_5
   6. Media_OHLC_PctChange_Lag_6
   7. Media_OHLC_PctChange_Lag_7
   8. Media_OHLC_PctChange_Lag_8
   9. Media_OHLC_PctChange_Lag_9
  10. Media_OHLC_PctChange_Lag_10
  11. Volume
  12. Spread
  13. Volatilidade

RESULTADOS DO MODELO:
  • Acurácia Geral: 0.491
  • Distribuição das Predições:
    - Sem Ação: 1 (0.0%)
    - Compra: 4551 (49.4%)
    - Venda: 4658 (50.6%)

DEFINIÇÃO DOS SINAIS:
  • Sinal de Compra: Média OHLC atual < Média OHLC 1 dias à frente
  • Sinal de Venda: Média OHLC atual > Média OHLC 1 dias à frente
  • Sem Ação: Casos onde não há sinal de compra nem venda

PARÂMETROS DO XGBOOST:
  • n_estimators: 100
  • max_depth: 6
  • learning_rate: 0.1
  • random_state: 42
  • eval_metric: logloss
  • objective: multi:softprob (adicionado automaticamente)
  • num_class: 3 (adicionado automaticamente)
  • eval_metric: mlogloss (adicionado automaticamente)
