RESUMO DO TREINAMENTO XGBOOST BINÁRIO - SINAIS DE TRADING
======================================================================

Data de treinamento: 2025-07-09 14:03:01

MODELO BINÁRIO:
  • Tipo: XGBoost Binário (sem classe 'Sem Ação')
  • Classes: 0=Venda, 1=Compra
  • Função de perda: Binary cross-entropy (logloss)
  • Threshold de probabilidade: 0.4 (sinais só gerados se prob > 0.4)
  • Features básicas: pct_change da média OHLC (variação percentual), Volume, Spread, Volatilidade
  • Features econométricas: Parkinson, Garman-Klass, MFI, EMV, Amihud, Roll Spread,
    Hurst, Vol/Volume, CMF, A/D Line, Volume Oscillator (12 features)
  • Total de features: 25
  • Acurácia geral: 0.595

CONFIGURAÇÕES UTILIZADAS:
  • Período de dados: 5y
  • Horizonte de sinais: 1 dias
  • Lags OHLC: 10
  • Janela volatilidade: 20
  • Multiplicador spread: 0.5

FEATURES UTILIZADAS (25):
   1. Media_OHLC_PctChange_Lag_1
   2. Media_OHLC_PctChange_Lag_2
   3. Media_OHLC_PctChange_Lag_3
   4. Media_OHLC_PctChange_Lag_4
   5. Media_OHLC_PctChange_Lag_5
   6. Media_OHLC_PctChange_Lag_6
   7. Media_OHLC_PctChange_Lag_7
   8. Media_OHLC_PctChange_Lag_8
   9. Media_OHLC_PctChange_Lag_9
  10. Media_OHLC_PctChange_Lag_10
  11. Volume
  12. Spread
  13. Volatilidade
  14. Parkinson_Volatility
  15. GK_Volatility
  16. MFI
  17. EMV
  18. EMV_MA
  19. Amihud
  20. Roll_Spread
  21. Hurst
  22. Vol_per_Volume
  23. CMF
  24. AD_Line
  25. VO

RESULTADOS DO MODELO:
  • Acurácia Geral: 0.595
  • Distribuição das Predições:
    - Venda: 4533 (49.4%)
    - Compra: 4640 (50.6%)

DEFINIÇÃO DOS SINAIS:
  • Sinal de Compra: Média OHLC atual < Média OHLC 1 dias à frente
  • Sinal de Venda: Média OHLC atual > Média OHLC 1 dias à frente
  • Sem Ação: Casos onde não há sinal de compra nem venda

PARÂMETROS DO XGBOOST:
  • n_estimators: 100
  • max_depth: 6
  • learning_rate: 0.1
  • random_state: 42
  • eval_metric: logloss
  • objective: multi:softprob (adicionado automaticamente)
  • num_class: 3 (adicionado automaticamente)
  • eval_metric: mlogloss (adicionado automaticamente)
